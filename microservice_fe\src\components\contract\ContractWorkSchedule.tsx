import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  useTheme,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import WorkIcon from '@mui/icons-material/Work';
import { CustomerContract } from '../../models';
import { calculateWorkingDates } from '../../utils/workingDaysUtils';
import { formatCurrency } from '../../utils/formatters';

// Mapping for Vietnamese day names
const dayNames: { [key: number]: string } = {
  1: 'Thứ Hai',
  2: 'Thứ Ba',
  3: '<PERSON><PERSON><PERSON>',
  4: '<PERSON><PERSON><PERSON>',
  5: '<PERSON><PERSON><PERSON>',
  6: 'T<PERSON><PERSON>',
  7: '<PERSON><PERSON>'
};

// Interface for shift schedule item
interface ShiftScheduleItem {
  date: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  numberOfWorkers: number;
  salary: number;
  jobCategoryName: string;
  dailyAmount: number;
}

interface ContractWorkScheduleProps {
  contract: CustomerContract;
}

const ContractWorkSchedule: React.FC<ContractWorkScheduleProps> = ({ contract }) => {
  const [expandedCategories, setExpandedCategories] = useState<{ [key: string]: boolean }>({});
  const theme = useTheme();

  const handleCategoryExpand = (categoryKey: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryKey]: !prev[categoryKey]
    }));
  };

  // Generate chronological shift schedule
  const generateShiftSchedule = () => {
    const allShifts: ShiftScheduleItem[] = [];

    contract.jobDetails.forEach(jobDetail => {
      jobDetail.workShifts.forEach(shift => {
        const workingDates = calculateWorkingDates(
          jobDetail.startDate,
          jobDetail.endDate,
          shift.workingDays
        );

        workingDates.forEach(dateStr => {
          // Parse date to get day of week
          const dateParts = dateStr.split('/');
          const date = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
          const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay();

          allShifts.push({
            date: dateStr,
            dayOfWeek: dayNames[dayOfWeek],
            startTime: shift.startTime,
            endTime: shift.endTime,
            numberOfWorkers: shift.numberOfWorkers,
            salary: shift.salary,
            jobCategoryName: jobDetail.jobCategoryName || '',
            dailyAmount: (shift.salary || 0) * (shift.numberOfWorkers || 0)
          });
        });
      });
    });

    // Sort by date and time
    return allShifts.sort((a, b) => {
      // First sort by date
      const dateA = new Date(a.date.split('/').reverse().join('-'));
      const dateB = new Date(b.date.split('/').reverse().join('-'));

      if (dateA.getTime() !== dateB.getTime()) {
        return dateA.getTime() - dateB.getTime();
      }

      // If same date, sort by start time
      const timeA = a.startTime.replace(':', '');
      const timeB = b.startTime.replace(':', '');
      return timeA.localeCompare(timeB);
    });
  };

  // Group shifts by job category
  const groupShiftsByCategory = (shifts: ShiftScheduleItem[]) => {
    const grouped: { [key: string]: ShiftScheduleItem[] } = {};

    shifts.forEach(shift => {
      if (!grouped[shift.jobCategoryName]) {
        grouped[shift.jobCategoryName] = [];
      }
      grouped[shift.jobCategoryName].push(shift);
    });

    return grouped;
  };

  const allShifts = generateShiftSchedule();
  const groupedShifts = groupShiftsByCategory(allShifts);

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <CalendarMonthIcon sx={{ mr: 1, color: theme.palette.primary.main, fontSize: 28 }} />
        <Typography variant="h5" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
          LỊCH LÀM VIỆC CHI TIẾT
        </Typography>
      </Box>

      {Object.entries(groupedShifts).map(([categoryName, shifts], categoryIndex) => {
        const categoryKey = `category-${categoryIndex}`;
        const totalCategoryAmount = shifts.reduce((sum, shift) => sum + shift.dailyAmount, 0);
        const totalCategoryDays = shifts.length;

        return (
          <Card
            key={categoryIndex}
            elevation={3}
            sx={{
              mb: 3,
              borderRadius: '12px',
              border: '2px solid',
              borderColor: theme.palette.primary.light,
              overflow: 'hidden',
            }}
          >
            <Accordion
              expanded={expandedCategories[categoryKey] || false}
              onChange={() => handleCategoryExpand(categoryKey)}
              sx={{ boxShadow: 'none', '&:before': { display: 'none' } }}
            >
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  p: 3,
                  backgroundColor: theme.palette.primary.light,
                  borderBottom: '1px solid',
                  borderColor: theme.palette.primary.main,
                  minHeight: '80px',
                }}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <WorkIcon sx={{ mr: 2, color: theme.palette.primary.main, fontSize: 32 }} />
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.primary.main }}>
                        {categoryName}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {totalCategoryDays} ca làm việc
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ textAlign: 'right' }}>
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                      {formatCurrency(totalCategoryAmount)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Tổng tiền danh mục
                    </Typography>
                  </Box>
                </Box>
              </AccordionSummary>

              <AccordionDetails sx={{ p: 0 }}>
                <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow sx={{ backgroundColor: theme.palette.grey[50] }}>
                        <TableCell sx={{ fontWeight: 'bold', minWidth: '60px' }}>STT</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', minWidth: '120px' }}>Ngày làm việc</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', minWidth: '100px' }}>Ca làm việc</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', minWidth: '100px' }}>Số nhân công</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', minWidth: '120px' }}>Lương/ngày</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', minWidth: '120px' }}>Thành tiền</TableCell>
                        <TableCell sx={{ fontWeight: 'bold', minWidth: '100px' }}>Trạng thái</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {shifts.map((shift, index) => (
                        <TableRow
                          key={index}
                          sx={{
                            '&:nth-of-type(odd)': { backgroundColor: theme.palette.action.hover },
                            '&:hover': { backgroundColor: theme.palette.primary.light }
                          }}
                        >
                          <TableCell>{index + 1}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <CalendarMonthIcon sx={{ mr: 1, fontSize: 16, color: theme.palette.primary.main }} />
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                                  {shift.dayOfWeek}, {shift.date}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <AccessTimeIcon sx={{ mr: 1, fontSize: 16, color: theme.palette.info.main }} />
                              <Typography variant="body2">
                                {shift.startTime} - {shift.endTime}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                              {shift.numberOfWorkers} người
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 'medium', color: theme.palette.success.main }}>
                              {formatCurrency(shift.salary)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" sx={{ fontWeight: 'bold', color: theme.palette.success.main }}>
                              {formatCurrency(shift.dailyAmount)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label="Chưa thực hiện"
                              color="warning"
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>
          </Card>
        );
      })}
    </Box>
  );
};

export default ContractWorkSchedule;
