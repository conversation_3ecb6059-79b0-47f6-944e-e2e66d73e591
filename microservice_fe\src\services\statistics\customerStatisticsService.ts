import { CustomerRevenue, CustomerPayment, TimeBasedRevenue, normalizeCustomerRevenue, normalizeTimeBasedRevenue } from '../../models';
import { get } from '../api/apiClient';
import axios from 'axios';
import { formatDateForInput } from '../../utils/dateUtils';

// Đảm bảo URL đúng, thêm kiểm tra môi trường
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8080';
const BASE_URL = '/api/customer-statistics';

// Hàm kiểm tra kết nối đến API
const checkApiConnection = async (): Promise<boolean> => {
  try {
    console.log(`Checking API connection to: ${API_BASE_URL}${BASE_URL}/health`);

    // Thử kết nối trực tiếp đến API Gateway
    const response = await axios.get(`${API_BASE_URL}${BASE_URL}/health`, {
      timeout: 5000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });

    console.log('API health check response:', response.status, response.data);
    return response.status === 200;
  } catch (error) {
    console.error('API connection check failed:', error);

    // Thử kết nối trực tiếp đến service
    try {
      console.log('Trying direct connection to service at http://localhost:8089/api/customer-statistics/health');
      const directResponse = await axios.get('http://localhost:8089/api/customer-statistics/health', {
        timeout: 5000,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      console.log('Direct API health check response:', directResponse.status, directResponse.data);
      return directResponse.status === 200;
    } catch (directError) {
      console.error('Direct API connection check failed:', directError);
      return false;
    }
  }
};

export const customerStatisticsService = {
  // Get customer revenue statistics
  getCustomerRevenueStatistics: async (startDate: Date, endDate: Date): Promise<CustomerRevenue[]> => {
    try {
      // Kiểm tra kết nối API trước khi gọi
      const isConnected = await checkApiConnection();
      if (!isConnected) {
        console.error('API connection failed. Service might be down.');
        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');
      }

      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      // Log chi tiết thông tin request
      console.log(`Calling API: ${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);
      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });

      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại
      let result;
      try {
        // Tăng timeout để tránh lỗi timeout
        result = await get<CustomerRevenue[]>(
          `${BASE_URL}/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000, // 30 second timeout
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          }
        );
      } catch (gatewayError) {
        console.error('Failed to get data through API Gateway:', gatewayError);
        console.log('Trying direct connection to service...');

        // Thử kết nối trực tiếp đến service
        const directResponse = await axios.get(
          `http://localhost:8089/api/customer-statistics/revenue?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        result = directResponse.data;
      }

      console.log('API result:', result);

      // Kiểm tra kết quả trả về
      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          // Xử lý từng phần tử một cách an toàn
          const normalizedData = result.map(customer => {
            // Đảm bảo customer không null
            if (!customer) return normalizeCustomerRevenue(null);
            return normalizeCustomerRevenue(customer);
          }).filter(customer => customer !== null);

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping customer revenue data:', err);
          return [];
        }
      }

      // If result is not an array, try to convert it
      if (typeof result === 'object') {
        console.warn('Result is not an array, attempting to convert:', result);
        try {
          // Nếu là object, thử chuyển thành array
          const singleItem = normalizeCustomerRevenue(result);
          return [singleItem];
        } catch (err) {
          console.error('Failed to convert object to array:', err);
        }
      }

      console.error('Invalid result format:', result);
      return [];
    } catch (error) {
      console.error('Error in getCustomerRevenueStatistics:', error);
      if (axios.isAxiosError(error)) {
        console.error('Response data:', error.response?.data);
        console.error('Response status:', error.response?.status);
        console.error('Request URL:', error.config?.url);

        // Kiểm tra lỗi CORS
        if (error.message.includes('Network Error') || !error.response) {
          console.error('Possible CORS or network issue');
        }
      }
      throw error;
    }
  },

  // Get customer invoices
  getCustomerInvoices: async (customerId: number, startDate: Date, endDate: Date): Promise<CustomerPayment[]> => {
    try {
      // Kiểm tra kết nối API trước khi gọi
      const isConnected = await checkApiConnection();
      if (!isConnected) {
        console.error('API connection failed. Service might be down.');
        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');
      }

      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      // Log chi tiết thông tin request
      console.log(`Calling API: ${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);
      console.log('Request parameters:', { customerId, startDate: formattedStartDate, endDate: formattedEndDate });

      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại
      let result;
      try {
        result = await get<CustomerPayment[]>(
          `${BASE_URL}/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000, // 30 second timeout
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          }
        );
      } catch (gatewayError) {
        console.error('Failed to get invoices through API Gateway:', gatewayError);
        console.log('Trying direct connection to service...');

        // Thử kết nối trực tiếp đến service
        const directResponse = await axios.get(
          `http://localhost:8089/api/customer-statistics/customer/${customerId}/invoices?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        result = directResponse.data;
      }

      console.log('Invoices result:', result);

      // Kiểm tra kết quả trả về
      if (!result) {
        console.error('API returned null or undefined result for invoices');
        return [];
      }

      // Ensure we have a valid array
      if (Array.isArray(result)) {
        // Xử lý dữ liệu trả về để đảm bảo các trường cần thiết
        return result.map(invoice => ({
          ...invoice,
          // Đảm bảo các trường quan trọng có giá trị mặc định nếu null
          id: invoice.id || 0,
          paymentCode: invoice.paymentCode || '',
          paymentAmount: typeof invoice.paymentAmount === 'number' ? invoice.paymentAmount : 0,
          contractCode: invoice.contractCode || 'Không xác định',
          paymentDate: invoice.paymentDate ? new Date(invoice.paymentDate) : new Date()
        })).filter(invoice => invoice !== null);
      }

      // If result is not an array but is an object, try to convert it
      if (typeof result === 'object' && result !== null) {
        console.warn('Invoices result is not an array, attempting to convert:', result);
        try {
          // Nếu là object, thử chuyển thành array
          return [result as CustomerPayment];
        } catch (err) {
          console.error('Failed to convert invoice object to array:', err);
        }
      }

      // If result is not an array, return empty array
      console.error('Invalid invoices result format:', result);
      return [];
    } catch (error) {
      console.error('Error in getCustomerInvoices:', error);
      if (axios.isAxiosError(error)) {
        console.error('Response data:', error.response?.data);
        console.error('Response status:', error.response?.status);
        console.error('Request URL:', error.config?.url);

        // Kiểm tra lỗi CORS
        if (error.message.includes('Network Error') || !error.response) {
          console.error('Possible CORS or network issue in getCustomerInvoices');
        }
      }
      throw error;
    }
  },

  // Get daily revenue statistics
  getDailyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      // Kiểm tra kết nối API trước khi gọi
      const isConnected = await checkApiConnection();
      if (!isConnected) {
        console.error('API connection failed. Service might be down.');
        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');
      }

      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      // Log chi tiết thông tin request
      console.log(`Calling API: ${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);
      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });

      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại
      let result;
      try {
        // Tăng timeout để tránh lỗi timeout
        result = await get<TimeBasedRevenue[]>(
          `${BASE_URL}/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000, // 30 second timeout
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          }
        );
      } catch (gatewayError) {
        console.error('Failed to get data through API Gateway:', gatewayError);
        console.log('Trying direct connection to service...');

        // Thử kết nối trực tiếp đến service
        const directResponse = await axios.get(
          `http://localhost:8089/api/customer-statistics/revenue/daily?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        result = directResponse.data;
      }

      console.log('API result:', result);

      // Kiểm tra kết quả trả về
      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          // Xử lý từng phần tử một cách an toàn
          const normalizedData = result.map(item => {
            // Đảm bảo item không null
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          })
          .filter(item => item !== null)
          // Sắp xếp theo ngày tăng dần
          .sort((a, b) => {
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateA.getTime() - dateB.getTime();
          });

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping daily revenue data:', err);
          return [];
        }
      }

      // Fallback if result is not an array
      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching daily revenue statistics:', error);
      throw error;
    }
  },

  // Get weekly revenue statistics
  getWeeklyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      // Kiểm tra kết nối API trước khi gọi
      const isConnected = await checkApiConnection();
      if (!isConnected) {
        console.error('API connection failed. Service might be down.');
        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');
      }

      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      // Log chi tiết thông tin request
      console.log(`Calling API: ${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);
      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });

      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại
      let result;
      try {
        // Tăng timeout để tránh lỗi timeout
        result = await get<TimeBasedRevenue[]>(
          `${BASE_URL}/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000, // 30 second timeout
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          }
        );
      } catch (gatewayError) {
        console.error('Failed to get data through API Gateway:', gatewayError);
        console.log('Trying direct connection to service...');

        // Thử kết nối trực tiếp đến service
        const directResponse = await axios.get(
          `http://localhost:8089/api/customer-statistics/revenue/weekly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        result = directResponse.data;
      }

      console.log('API result:', result);

      // Kiểm tra kết quả trả về
      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          // Xử lý từng phần tử một cách an toàn
          const normalizedData = result.map(item => {
            // Đảm bảo item không null
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          }).filter(item => item !== null);

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping weekly revenue data:', err);
          return [];
        }
      }

      // Fallback if result is not an array
      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching weekly revenue statistics:', error);
      throw error;
    }
  },

  // Get monthly revenue statistics
  getMonthlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      // Kiểm tra kết nối API trước khi gọi
      const isConnected = await checkApiConnection();
      if (!isConnected) {
        console.error('API connection failed. Service might be down.');
        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');
      }

      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      // Log chi tiết thông tin request
      console.log(`Calling API: ${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);
      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });

      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại
      let result;
      try {
        // Tăng timeout để tránh lỗi timeout
        result = await get<TimeBasedRevenue[]>(
          `${BASE_URL}/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000, // 30 second timeout
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          }
        );
      } catch (gatewayError) {
        console.error('Failed to get data through API Gateway:', gatewayError);
        console.log('Trying direct connection to service...');

        // Thử kết nối trực tiếp đến service
        const directResponse = await axios.get(
          `http://localhost:8089/api/customer-statistics/revenue/monthly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        result = directResponse.data;
      }

      console.log('API result:', result);

      // Kiểm tra kết quả trả về
      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          // Xử lý từng phần tử một cách an toàn
          const normalizedData = result.map(item => {
            // Đảm bảo item không null
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          })
          .filter(item => item !== null)
          // Sắp xếp theo ngày tăng dần
          .sort((a, b) => {
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateA.getTime() - dateB.getTime();
          });

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping monthly revenue data:', err);
          return [];
        }
      }

      // Fallback if result is not an array
      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching monthly revenue statistics:', error);
      throw error;
    }
  },

  // Get yearly revenue statistics
  getYearlyRevenueStatistics: async (startDate: Date, endDate: Date): Promise<TimeBasedRevenue[]> => {
    try {
      // Kiểm tra kết nối API trước khi gọi
      const isConnected = await checkApiConnection();
      if (!isConnected) {
        console.error('API connection failed. Service might be down.');
        throw new Error('Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.');
      }

      const formattedStartDate = formatDateToString(startDate);
      const formattedEndDate = formatDateToString(endDate);

      // Log chi tiết thông tin request
      console.log(`Calling API: ${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`);
      console.log('Request parameters:', { startDate: formattedStartDate, endDate: formattedEndDate });

      // Thử gọi API trực tiếp nếu gọi qua API Gateway thất bại
      let result;
      try {
        // Tăng timeout để tránh lỗi timeout
        result = await get<TimeBasedRevenue[]>(
          `${BASE_URL}/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000, // 30 second timeout
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            }
          }
        );
      } catch (gatewayError) {
        console.error('Failed to get data through API Gateway:', gatewayError);
        console.log('Trying direct connection to service...');

        // Thử kết nối trực tiếp đến service
        const directResponse = await axios.get(
          `http://localhost:8089/api/customer-statistics/revenue/yearly?startDate=${formattedStartDate}&endDate=${formattedEndDate}`,
          {
            timeout: 30000,
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          }
        );

        result = directResponse.data;
      }

      console.log('API result:', result);

      // Kiểm tra kết quả trả về
      if (!result) {
        console.error('API returned null or undefined result');
        return [];
      }

      // Normalize the data using our helper function
      if (Array.isArray(result)) {
        try {
          // Xử lý từng phần tử một cách an toàn
          const normalizedData = result.map(item => {
            // Đảm bảo item không null
            if (!item) return normalizeTimeBasedRevenue(null);
            return normalizeTimeBasedRevenue(item);
          })
          .filter(item => item !== null)
          // Sắp xếp theo năm tăng dần
          .sort((a, b) => {
            const dateA = new Date(a.date);
            const dateB = new Date(b.date);
            return dateA.getTime() - dateB.getTime();
          });

          console.log('Normalized data:', normalizedData);
          return normalizedData;
        } catch (err) {
          console.error('Error mapping yearly revenue data:', err);
          return [];
        }
      }

      // Fallback if result is not an array
      console.error('API result is not an array:', result);
      return [];
    } catch (error) {
      console.error('Error fetching yearly revenue statistics:', error);
      throw error;
    }
  }
};

// Helper function to format date to string in ISO format (yyyy-MM-dd)
const formatDateToString = (date: Date): string => {
  try {
    // Ensure we have a valid date
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      console.error('Invalid date provided:', date);
      // Return today's date as fallback
      const today = new Date();
      return formatDateForInput(today);
    }

    // Xử lý đặc biệt để tránh vấn đề múi giờ
    // Đặt giờ là 12:00:00 để tránh vấn đề chuyển đổi múi giờ
    const adjustedDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      12, 0, 0
    );

    // Format to ISO date string using our utility function
    return formatDateForInput(adjustedDate);
  } catch (error) {
    console.error('Error formatting date:', error);
    // Return today's date as fallback
    const today = new Date();
    return formatDateForInput(today);
  }
};
